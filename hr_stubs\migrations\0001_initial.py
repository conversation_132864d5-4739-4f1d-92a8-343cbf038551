# Generated by Django 5.0.14 on 2025-06-22 13:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Car',
            fields=[
                ('car_code', models.IntegerField(primary_key=True, serialize=False, verbose_name='رمز السيارة')),
                ('car_name', models.CharField(max_length=100, verbose_name='اسم السيارة')),
            ],
            options={
                'verbose_name': 'سيارة',
                'verbose_name_plural': 'السيارات',
                'db_table': 'hr_stubs_car',
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('dept_code', models.IntegerField(primary_key=True, serialize=False, verbose_name='رمز القسم')),
                ('dept_name', models.Char<PERSON>ield(max_length=50, verbose_name='اسم القسم')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'db_table': 'hr_stubs_department',
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('emp_code', models.IntegerField(primary_key=True, serialize=False, verbose_name='رمز الموظف')),
                ('emp_name', models.CharField(max_length=100, verbose_name='اسم الموظف')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='hr_stubs.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'موظف',
                'verbose_name_plural': 'الموظفين',
                'db_table': 'hr_stubs_employee',
            },
        ),
        migrations.CreateModel(
            name='EmployeeLeave',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('status', models.CharField(default='pending', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leaves', to='hr_stubs.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'إجازة الموظف',
                'verbose_name_plural': 'إجازات الموظفين',
                'db_table': 'hr_stubs_employeeleave',
            },
        ),
        migrations.CreateModel(
            name='EmployeeTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المهمة')),
                ('status', models.CharField(default='pending', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='hr_stubs.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'مهمة الموظف',
                'verbose_name_plural': 'مهام الموظفين',
                'db_table': 'hr_stubs_employeetask',
            },
        ),
        migrations.CreateModel(
            name='TaskStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الخطوة')),
                ('status', models.CharField(default='pending', max_length=20, verbose_name='الحالة')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='hr_stubs.employeetask', verbose_name='المهمة')),
            ],
            options={
                'verbose_name': 'خطوة المهمة',
                'verbose_name_plural': 'خطوات المهام',
                'db_table': 'hr_stubs_taskstep',
            },
        ),
    ]
