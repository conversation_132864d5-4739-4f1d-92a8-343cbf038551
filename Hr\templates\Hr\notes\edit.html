{% extends 'Hr/base_hr.html' %}
{% load static %}
{% load image_utils %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .employee-info-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border-radius: 15px;
        color: white;
    }
    
    .form-section {
        background: #f8f9fa;
        border-radius: 15px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .form-section:focus-within {
        border-color: #007bff;
        box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
    }
    
    .employee-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid rgba(255, 255, 255, 0.3);
    }
    
    .form-floating label {
        color: #6c757d;
    }
    
    .btn-save {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
    }
    
    .note-preview {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        min-height: 200px;
        white-space: pre-wrap;
    }
    
    .changes-indicator {
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1050;
        display: none;
    }
</style>
{% endblock %}

{% block page_title %}
<div class="d-flex align-items-center justify-content-between">
    <div>
        <h1 class="h3 mb-1 text-gray-800">
            <i class="fas fa-edit text-primary me-2"></i>
            {{ title }}
        </h1>
        <p class="text-muted mb-0">تعديل ملاحظة الموظف</p>
    </div>
    <div>
        <a href="{% url 'Hr:notes:detail' note.id %}" class="btn btn-outline-secondary me-2">
            <i class="fas fa-arrow-right me-2"></i>
            العودة للملاحظة
        </a>
        <a href="{% url 'Hr:notes:employee_notes' note.employee.emp_id %}" class="btn btn-outline-info">
            <i class="fas fa-list me-2"></i>
            جميع ملاحظات الموظف
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Changes Indicator -->
<div class="changes-indicator">
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تنبيه:</strong> لديك تغييرات غير محفوظة
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>

<div class="row">
    <!-- Employee Information -->
    <div class="col-12 mb-4">
        <div class="employee-info-header p-4">
            <div class="d-flex align-items-center">
                <div class="me-4">
                    {% if employee.emp_image %}
                    <img src="{{ employee.emp_image|binary_to_img }}" 
                         alt="{{ employee.emp_full_name }}"
                         class="employee-avatar">
                    {% else %}
                    <div class="employee-avatar bg-white bg-opacity-20 d-flex align-items-center justify-content-center text-white"
                         style="font-size: 2rem; font-weight: bold;">
                        {{ employee.emp_first_name|slice:":1"|upper }}
                    </div>
                    {% endif %}
                </div>
                <div class="text-white">
                    <h3 class="mb-1">{{ employee.emp_full_name|default:employee.emp_first_name }}</h3>
                    <p class="mb-1 opacity-75">{{ employee.jop_name|default:"غير محدد" }}</p>
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-building me-1"></i>
                        {{ employee.department.dept_name|default:"غير محدد" }}
                        <span class="ms-3">
                            <i class="fas fa-id-badge me-1"></i>
                            رقم الموظف: {{ employee.emp_id }}
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Edit Form -->
    <div class="col-lg-8 mb-4">
        <div class="form-section p-4">
            <h5 class="mb-4">
                <i class="fas fa-edit me-2"></i>
                تعديل الملاحظة
            </h5>
            
            <form method="post" id="editForm">
                {% csrf_token %}
                
                <div class="row g-3">
                    <div class="col-12">
                        <div class="form-floating">
                            {{ form.title }}
                            <label for="{{ form.title.id_for_label }}">{{ form.title.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ form.note_type }}
                            <label for="{{ form.note_type.id_for_label }}">{{ form.note_type.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ form.priority }}
                            <label for="{{ form.priority.id_for_label }}">{{ form.priority.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="form-floating">
                            {{ form.content }}
                            <label for="{{ form.content.id_for_label }}">{{ form.content.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ form.evaluation_link }}
                            <label for="{{ form.evaluation_link.id_for_label }}">{{ form.evaluation_link.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-floating">
                            {{ form.evaluation_score }}
                            <label for="{{ form.evaluation_score.id_for_label }}">{{ form.evaluation_score.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <div class="form-floating">
                            {{ form.tags }}
                            <label for="{{ form.tags.id_for_label }}">{{ form.tags.label }}</label>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="form-check form-switch">
                            {{ form.is_important }}
                            <label class="form-check-label" for="{{ form.is_important.id_for_label }}">
                                {{ form.is_important.label }}
                            </label>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="form-check form-switch">
                            {{ form.is_confidential }}
                            <label class="form-check-label" for="{{ form.is_confidential.id_for_label }}">
                                {{ form.is_confidential.label }}
                            </label>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="form-check form-switch">
                            {{ form.follow_up_required }}
                            <label class="form-check-label" for="{{ form.follow_up_required.id_for_label }}">
                                {{ form.follow_up_required.label }}
                            </label>
                        </div>
                    </div>
                    
                    <div class="col-md-6" id="followUpDateField" style="{% if not form.follow_up_required.value %}display: none;{% endif %}">
                        <div class="form-floating">
                            {{ form.follow_up_date }}
                            <label for="{{ form.follow_up_date.id_for_label }}">{{ form.follow_up_date.label }}</label>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            آخر تعديل: {{ note.updated_at|date:"Y/m/d H:i" }}
                        </small>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>
                            إعادة تعيين
                        </button>
                        <button type="submit" class="btn btn-save text-white">
                            <i class="fas fa-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Preview and Info -->
    <div class="col-lg-4">
        <!-- Note Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    معاينة الملاحظة
                </h6>
            </div>
            <div class="card-body">
                <h6 id="previewTitle">{{ note.title }}</h6>
                <div class="mb-2">
                    <span class="badge bg-primary" id="previewType">{{ note.get_note_type_display }}</span>
                    <span class="badge bg-secondary" id="previewPriority">{{ note.get_priority_display }}</span>
                </div>
                <div class="note-preview" id="previewContent">{{ note.content }}</div>
            </div>
        </div>
        
        <!-- Original Note Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الملاحظة الأصلية
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>تم الإنشاء بواسطة:</strong><br>
                    <span class="text-muted">{{ note.created_by.get_full_name|default:note.created_by.username }}</span>
                </div>
                <div class="mb-2">
                    <strong>تاريخ الإنشاء:</strong><br>
                    <span class="text-muted">{{ note.created_at|date:"Y/m/d H:i" }}</span>
                </div>
                {% if note.last_modified_by and note.updated_at != note.created_at %}
                <div class="mb-2">
                    <strong>آخر تعديل بواسطة:</strong><br>
                    <span class="text-muted">{{ note.last_modified_by.get_full_name|default:note.last_modified_by.username }}</span>
                </div>
                <div class="mb-2">
                    <strong>تاريخ آخر تعديل:</strong><br>
                    <span class="text-muted">{{ note.updated_at|date:"Y/m/d H:i" }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'Hr:notes:detail' note.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>
                        عرض الملاحظة
                    </a>
                    <a href="{% url 'Hr:notes:create' %}?employee_id={{ employee.emp_id }}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة ملاحظة جديدة
                    </a>
                    <a href="{% url 'Hr:notes:employee_notes' employee.emp_id %}" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>
                        جميع ملاحظات الموظف
                    </a>
                    <hr>
                    <a href="{% url 'Hr:notes:delete' note.id %}" class="btn btn-outline-danger">
                        <i class="fas fa-trash me-2"></i>
                        حذف الملاحظة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editForm');
    const followUpRequired = document.getElementById('{{ form.follow_up_required.id_for_label }}');
    const followUpDateField = document.getElementById('followUpDateField');
    const changesIndicator = document.querySelector('.changes-indicator');
    
    // Store original form data
    const originalData = new FormData(form);
    let hasChanges = false;
    
    // Handle follow-up date visibility
    if (followUpRequired) {
        followUpRequired.addEventListener('change', function() {
            if (this.checked) {
                followUpDateField.style.display = 'block';
            } else {
                followUpDateField.style.display = 'none';
            }
            checkForChanges();
        });
    }
    
    // Monitor form changes
    form.addEventListener('input', checkForChanges);
    form.addEventListener('change', checkForChanges);
    
    // Update preview
    const titleInput = document.getElementById('{{ form.title.id_for_label }}');
    const contentInput = document.getElementById('{{ form.content.id_for_label }}');
    const typeInput = document.getElementById('{{ form.note_type.id_for_label }}');
    const priorityInput = document.getElementById('{{ form.priority.id_for_label }}');
    
    if (titleInput) {
        titleInput.addEventListener('input', function() {
            document.getElementById('previewTitle').textContent = this.value || 'عنوان الملاحظة';
        });
    }
    
    if (contentInput) {
        contentInput.addEventListener('input', function() {
            document.getElementById('previewContent').textContent = this.value || 'محتوى الملاحظة...';
        });
    }
    
    if (typeInput) {
        typeInput.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            document.getElementById('previewType').textContent = selectedOption.text;
        });
    }
    
    if (priorityInput) {
        priorityInput.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            document.getElementById('previewPriority').textContent = selectedOption.text;
        });
    }
    
    function checkForChanges() {
        const currentData = new FormData(form);
        hasChanges = false;
        
        for (let [key, value] of currentData.entries()) {
            if (originalData.get(key) !== value) {
                hasChanges = true;
                break;
            }
        }
        
        if (hasChanges) {
            changesIndicator.style.display = 'block';
        } else {
            changesIndicator.style.display = 'none';
        }
    }
    
    // Warn before leaving with unsaved changes
    window.addEventListener('beforeunload', function(e) {
        if (hasChanges) {
            e.preventDefault();
            e.returnValue = 'لديك تغييرات غير محفوظة. هل تريد المغادرة؟';
        }
    });
    
    // Reset form function
    window.resetForm = function() {
        if (confirm('هل تريد إعادة تعيين النموذج؟ ستفقد جميع التغييرات غير المحفوظة.')) {
            form.reset();
            followUpDateField.style.display = followUpRequired.checked ? 'block' : 'none';
            hasChanges = false;
            changesIndicator.style.display = 'none';
            
            // Reset preview
            document.getElementById('previewTitle').textContent = '{{ note.title }}';
            document.getElementById('previewContent').textContent = '{{ note.content }}';
        }
    };
    
    // Form submission
    form.addEventListener('submit', function() {
        hasChanges = false; // Prevent warning on successful submit
    });
});
</script>
{% endblock %}
