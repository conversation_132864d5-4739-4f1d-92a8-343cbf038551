{% extends 'Hr/base.html' %}
{% load static %}

{% block title %}إدارة الشركات - هيكل الشركة - ElDawliya{% endblock %}

{% block page_title %}
    <i class="fas fa-building me-2"></i>
    إدارة الشركات
{% endblock %}

{% block header_actions %}
    <div class="btn-group" role="group">
        <a href="{% url 'hr:company_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            إضافة شركة جديدة
        </a>
        <a href="{% url 'hr:branch_list' %}" class="btn btn-outline-info">
            <i class="fas fa-code-branch"></i>
            الفروع
        </a>
        <a href="{% url 'hr:department_list' %}" class="btn btn-outline-success">
            <i class="fas fa-sitemap"></i>
            الأقسام
        </a>
        <button type="button" class="btn btn-outline-warning" onclick="exportCompanies()">
            <i class="fas fa-file-export"></i>
            تصدير
        </button>
    </div>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-primary border-4">
            <div class="stats-number text-primary">{{ total_companies }}</div>
            <div class="stats-label">إجمالي الشركات</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-success border-4">
            <div class="stats-number text-success">{{ active_companies }}</div>
            <div class="stats-label">الشركات النشطة</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-info border-4">
            <div class="stats-number text-info">{{ total_branches }}</div>
            <div class="stats-label">إجمالي الفروع</div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card border-start border-warning border-4">
            <div class="stats-number text-warning">{{ total_employees }}</div>
            <div class="stats-label">إجمالي الموظفين</div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            البحث والتصفية
        </h5>
    </div>
    <div class="card-body">
        <form method="get" id="filterForm">
            <div class="row g-3">
                <!-- Search -->
                <div class="col-lg-4 col-md-6">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search_value }}" placeholder="اسم الشركة، الكود، العنوان...">
                </div>

                <!-- Status Filter -->
                <div class="col-lg-2 col-md-6">
                    <label for="is_active" class="form-label">الحالة</label>
                    <select class="form-select" id="is_active" name="is_active">
                        <option value="">جميع الحالات</option>
                        <option value="true" {% if is_active_filter == 'true' %}selected{% endif %}>نشطة</option>
                        <option value="false" {% if is_active_filter == 'false' %}selected{% endif %}>غير نشطة</option>
                    </select>
                </div>

                <!-- City Filter -->
                <div class="col-lg-3 col-md-6">
                    <label for="city" class="form-label">المدينة</label>
                    <select class="form-select" id="city" name="city">
                        <option value="">جميع المدن</option>
                        {% for city in cities %}
                            <option value="{{ city }}" {% if city_filter == city %}selected{% endif %}>
                                {{ city }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Country Filter -->
                <div class="col-lg-3 col-md-6">
                    <label for="country" class="form-label">البلد</label>
                    <select class="form-select" id="country" name="country">
                        <option value="">جميع البلدان</option>
                        {% for country in countries %}
                            <option value="{{ country }}" {% if country_filter == country %}selected{% endif %}>
                                {{ country }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="fas fa-eraser"></i>
                        مسح الفلاتر
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="filterActive()">
                        <i class="fas fa-check-circle"></i>
                        النشطة فقط
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Companies Grid -->
<div class="row">
    {% if companies %}
        {% for company in companies %}
        <div class="col-lg-6 col-xl-4 mb-4">
            <div class="card h-100 company-card border-start border-{% if company.is_active %}success{% else %}secondary{% endif %} border-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="flex-grow-1">
                            <h5 class="card-title mb-1">
                                <a href="{% url 'hr:company_detail' company.pk %}" class="text-decoration-none">
                                    {{ company.name }}
                                </a>
                            </h5>
                            <p class="text-muted small mb-0">{{ company.code }}</p>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-{% if company.is_active %}success{% else %}secondary{% endif %}">
                                {% if company.is_active %}نشطة{% else %}غير نشطة{% endif %}
                            </span>
                        </div>
                    </div>

                    {% if company.logo %}
                        <div class="text-center mb-3">
                            <img src="{{ company.logo.url }}" alt="{{ company.name }}" 
                                 class="img-fluid rounded" style="max-height: 80px;">
                        </div>
                    {% endif %}

                    <div class="company-info">
                        {% if company.address %}
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-map-marker-alt text-muted me-2"></i>
                                <small class="text-muted">{{ company.address|truncatechars:40 }}</small>
                            </div>
                        {% endif %}

                        {% if company.phone %}
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-phone text-muted me-2"></i>
                                <small class="text-muted">{{ company.phone }}</small>
                            </div>
                        {% endif %}

                        {% if company.email %}
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-envelope text-muted me-2"></i>
                                <small class="text-muted">{{ company.email }}</small>
                            </div>
                        {% endif %}

                        {% if company.website %}
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-globe text-muted me-2"></i>
                                <small>
                                    <a href="{{ company.website }}" target="_blank" class="text-decoration-none">
                                        {{ company.website|truncatechars:30 }}
                                    </a>
                                </small>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Statistics -->
                    <div class="row text-center mt-3 pt-3 border-top">
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number text-primary">{{ company.branches_count|default:0 }}</div>
                                <div class="stat-label">فرع</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number text-success">{{ company.departments_count|default:0 }}</div>
                                <div class="stat-label">قسم</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number text-info">{{ company.employees_count|default:0 }}</div>
                                <div class="stat-label">موظف</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer bg-transparent">
                    <div class="btn-group w-100" role="group">
                        <a href="{% url 'hr:company_detail' company.pk %}" 
                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{% url 'hr:company_update' company.pk %}" 
                           class="btn btn-sm btn-outline-warning" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-info" 
                                onclick="viewBranches({{ company.id }})" title="الفروع">
                            <i class="fas fa-code-branch"></i>
                        </button>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{% url 'hr:company_employees' company.pk %}">
                                        <i class="fas fa-users me-2"></i>
                                        الموظفين
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'hr:company_departments' company.pk %}">
                                        <i class="fas fa-sitemap me-2"></i>
                                        الأقسام
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'hr:company_statistics' company.pk %}">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        الإحصائيات
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button class="dropdown-item" onclick="toggleStatus({{ company.id }})">
                                        <i class="fas fa-{% if company.is_active %}pause{% else %}play{% endif %} me-2"></i>
                                        {% if company.is_active %}إلغاء التفعيل{% else %}تفعيل{% endif %}
                                    </button>
                                </li>
                                <li>
                                    <button class="dropdown-item" onclick="duplicateCompany({{ company.id }})">
                                        <i class="fas fa-copy me-2"></i>
                                        نسخ
                                    </button>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button class="dropdown-item text-danger" 
                                            onclick="deleteCompany({{ company.id }}, '{{ company.name }}')">
                                        <i class="fas fa-trash me-2"></i>
                                        حذف
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}

        <!-- Pagination -->
        {% if is_paginated %}
        <div class="col-12">
            <nav aria-label="تنقل الصفحات">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد شركات</h5>
                <p class="text-muted">لم يتم العثور على شركات مطابقة لمعايير البحث</p>
                <a href="{% url 'hr:company_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة أول شركة
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
.company-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.company-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.stat-item {
    padding: 0.5rem 0;
}

.stat-number {
    font-size: 1.25rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.company-info {
    min-height: 120px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Filter functions
function clearFilters() {
    $('#filterForm')[0].reset();
    window.location.href = '{% url "hr:company_list" %}';
}

function filterActive() {
    $('#is_active').val('true');
    $('#filterForm').submit();
}

// Company operations
function viewBranches(companyId) {
    window.location.href = `/hr/company/branches/?company=${companyId}`;
}

function toggleStatus(companyId) {
    $.post(`/hr/company/companies/${companyId}/toggle-status/`, {
        csrfmiddlewaretoken: '{{ csrf_token }}'
    }, function(response) {
        if (response.success) {
            showAlert('تم تغيير حالة الشركة بنجاح', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(response.error, 'danger');
        }
    });
}

function duplicateCompany(companyId) {
    if (confirm('هل تريد نسخ هذه الشركة؟')) {
        $.post(`/hr/company/companies/${companyId}/duplicate/`, {
            csrfmiddlewaretoken: '{{ csrf_token }}'
        }, function(response) {
            if (response.success) {
                showAlert('تم نسخ الشركة بنجاح', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert(response.error, 'danger');
            }
        });
    }
}

function deleteCompany(companyId, companyName) {
    if (confirm(`هل أنت متأكد من حذف الشركة "${companyName}"؟\n\nتحذير: سيتم حذف جميع الفروع والأقسام والموظفين المرتبطين بهذه الشركة.\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        $.post(`/hr/company/companies/${companyId}/delete/`, {
            csrfmiddlewaretoken: '{{ csrf_token }}'
        }, function(response) {
            if (response.success) {
                showAlert('تم حذف الشركة بنجاح', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert(response.error, 'danger');
            }
        });
    }
}

function exportCompanies() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open(`${window.location.pathname}?${params.toString()}`);
}

// Show alert
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.main-content').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
