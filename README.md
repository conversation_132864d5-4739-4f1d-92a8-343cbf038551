# نظام الدولية (ElDawliya System)

## نظرة عامة
نظام الدولية هو منصة متكاملة لإدارة العمليات الداخلية للمؤسسة، يتضمن مجموعة من الوحدات المترابطة لتغطية مختلف احتياجات الإدارة والتشغيل.

## الوحدات الرئيسية

### 1. نظام الموارد البشرية (HR)
- إدارة بيانات الموظفين
- إدارة الإجازات والغياب
- متابعة العقود والوثائق
- إدارة الرواتب والمستحقات

### 2. نظام مهام الموظفين (Employee Tasks)
- إنشاء وتعديل وحذف المهام الشخصية
- تصنيف المهام حسب الأولوية والحالة
- تتبع نسبة إنجاز المهام
- إضافة خطوات تفصيلية لكل مهمة
- عرض المهام في تقويم وتحليلات أداء

### 3. نظام الاجتماعات (Meetings)
- جدولة وإدارة الاجتماعات
- دعوة المشاركين ومتابعة الحضور
- توثيق محاضر الاجتماعات
- متابعة القرارات والتوصيات

### 4. نظام المخزون (Inventory)
- إدارة المنتجات والأصناف
- متابعة حركة المخزون
- إنشاء طلبات الشراء التلقائية
- تنبيهات الحد الأدنى للمخزون

### 5. نظام المشتريات (Purchase Orders)
- إدارة طلبات الشراء
- متابعة حالة الطلبات
- إدارة الموافقات والمراجعات
- ربط الطلبات بالمخزون

### 6. نظام API والذكاء الاصطناعي (API & AI)
- **REST API شامل** لجميع وظائف النظام
- **دمج Google Gemini AI** للمحادثات الذكية وتحليل البيانات
- **مصادقة متعددة**: API Keys, JWT Tokens, Session Auth
- **وثائق تفاعلية** مع Swagger UI و ReDoc
- **تحليل البيانات بالذكاء الاصطناعي** واستخراج الرؤى
- **نظام صلاحيات متقدم** حسب المجموعات
- **مراقبة الاستخدام** وإحصائيات الأداء

## الأنظمة المساندة

### 1. نظام الأذونات (Permissions)
- نظام أذونات هرمي (قسم > وحدة > صلاحية)
- نظام RBAC للأدوار والصلاحيات
- تحكم دقيق بصلاحيات المستخدمين

### 2. نظام التنبيهات (Notifications)
- تنبيهات مخصصة لكل وحدة
- مستويات أولوية متعددة
- إشعارات فورية للمستخدمين

### 3. نظام تسجيل الأحداث (Audit)
- تسجيل تلقائي للأحداث
- تتبع عمليات المستخدمين
- سجلات تفصيلية للتغييرات

## المتطلبات التقنية
- Python 3.7+
- Django 3.2+
- قاعدة بيانات SQL Server أو SQLite
- JavaScript (jQuery, FullCalendar.js, Chart.js)
- Bootstrap 5

## التثبيت والإعداد
1. استنساخ المستودع:
```
git clone https://github.com/your-organization/ElDawliya_Sys.git
```

2. تثبيت المتطلبات:
```
pip install -r requirements.txt
```

3. إعداد قاعدة البيانات:
```
python manage.py migrate
```

4. إنشاء مستخدم مشرف:
```
python manage.py createsuperuser
```

5. إعداد API والذكاء الاصطناعي (تلقائي):
```
python setup_api.py
```

6. تشغيل الخادم مع API:
```
python run_api_server.py
```

## استخدام API

### الوصول للوثائق التفاعلية
- **Swagger UI**: http://localhost:8000/api/v1/docs/
- **ReDoc**: http://localhost:8000/api/v1/redoc/
- **حالة API**: http://localhost:8000/api/v1/status/

### المصادقة
```bash
# باستخدام API Key
curl -H "Authorization: ApiKey YOUR_API_KEY" http://localhost:8000/api/v1/employees/

# باستخدام JWT Token
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" http://localhost:8000/api/v1/products/
```

### أمثلة الاستخدام

#### محادثة مع الذكاء الاصطناعي
```bash
curl -X POST http://localhost:8000/api/v1/ai/chat/ \
  -H "Authorization: ApiKey YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"message": "ما هو عدد الموظفين في النظام؟"}'
```

#### تحليل بيانات الموظفين
```bash
curl -X POST http://localhost:8000/api/v1/ai/analyze/ \
  -H "Authorization: ApiKey YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"data_type": "employees", "analysis_type": "summary"}'
```

#### البحث في المنتجات
```bash
curl "http://localhost:8000/api/v1/products/?search=كمبيوتر&low_stock=true" \
  -H "Authorization: ApiKey YOUR_API_KEY"
```

### إدارة API

#### إنشاء مفتاح API
```bash
python manage.py create_api_key username --name "My API Key" --expires-days 30
```

#### إعداد مجموعات المستخدمين
```bash
python manage.py setup_api_groups
```

#### تشغيل اختبارات API
```bash
python manage.py test api
```

### أمثلة متقدمة
```bash
# تشغيل أمثلة شاملة للـ API
python api_examples.py
```

## التطبيقات والوحدات التفصيلية (Detailed Applications & Modules)

### 📋 التطبيقات الأساسية (Core Applications)

#### 1. تطبيق الموارد البشرية (Hr Application)
**المسار**: `Hr/` | **التوثيق**: [Hr/README.md](Hr/README.md)
- إدارة شاملة للموظفين مع ملفات تفصيلية
- نظام الحضور والانصراف المتقدم مع أجهزة متعددة
- إدارة الإجازات والطلبات مع سير عمل الموافقات
- نظام الرواتب المرن مع بنود قابلة للتخصيص
- هيكل الشركة والأقسام والوظائف
- تقييمات الأداء وملاحظات الموظفين

#### 2. تطبيق إدارة الحسابات (Accounts Application)
**المسار**: `accounts/` | **التوثيق**: [accounts/README.md](accounts/README.md)
- نظام مصادقة آمن مع تشفير متقدم
- إدارة المستخدمين والأدوار والصلاحيات
- نظام الجلسات والأمان المتقدم
- لوحة تحكم المستخدمين مع إحصائيات
- تكامل مع نظام الصلاحيات الهرمي

#### 3. تطبيق إدارة النظام (Administrator Application)
**المسار**: `administrator/` | **التوثيق**: [administrator/README.md](administrator/README.md)
- إعدادات النظام العامة وقاعدة البيانات
- نظام الصلاحيات المتقدم على مستوى الوحدات
- إدارة الأقسام والوحدات والمجموعات
- مراقبة النظام والأداء مع تنبيهات
- أدوات الصيانة والنسخ الاحتياطي

#### 4. تطبيق واجهة برمجة التطبيقات (API Application)
**المسار**: `api/` | **التوثيق**: [api/README.md](api/README.md)
- REST API شامل لجميع وحدات النظام
- تكامل Google Gemini للذكاء الاصطناعي
- إدارة مفاتيح API مع تحكم في الصلاحيات
- تسجيل الاستخدام والمراقبة المتقدمة
- واجهة ويب تفاعلية لاختبار API

### 🏢 التطبيقات التشغيلية (Operational Applications)

#### 5. تطبيق المخزون (Inventory Application)
**المسار**: `inventory/` | **التوثيق**: [inventory/README.md](inventory/README.md)
- إدارة شاملة لمخزون قطع الغيار
- تتبع الكميات والحركات مع تنبيهات المخزون المنخفض
- إدارة الموردين والعملاء مع تاريخ التعاملات
- نظام الفواتير والأذونات المتكامل
- تقارير المخزون التفصيلية والتحليلات

#### 6. نظام مهام الموظفين (Employee Tasks System)
**المسار**: `employee_tasks/` | **التوثيق**: [employee_tasks/README.md](employee_tasks/README.md)
- إدارة المهام الشخصية مع خطوات تفصيلية
- تتبع نسبة الإنجاز والتقدم التلقائي
- نظام التذكيرات والتنبيهات المجدولة
- تصنيف المهام مع ألوان وأيقونات مخصصة
- عرض المهام في تقويم تفاعلي
- تحليلات الأداء والإنتاجية

#### 7. تطبيق إدارة الاجتماعات (Meetings Application)
**المسار**: `meetings/` | **التوثيق**: [meetings/README.md](meetings/README.md)
- جدولة الاجتماعات مع إدارة المشاركين
- إدارة جدول الأعمال مع تقدير الأوقات
- تسجيل محاضر الاجتماعات والقرارات
- تتبع الحضور والمتابعة
- حجز قاعات الاجتماعات مع فحص التوفر

### 🔍 التطبيقات المساندة (Supporting Applications)

#### 8. تطبيق سجلات التدقيق (Audit Application)
**المسار**: `audit/` | **التوثيق**: [audit/README.md](audit/README.md)
- تسجيل تلقائي لجميع العمليات والأنشطة
- تتبع تغييرات البيانات مع تفاصيل كاملة
- معلومات مفصلة عن المستخدم والجلسة
- بحث وفلترة متقدمة في السجلات
- تقارير الأمان والامتثال

#### 9. تطبيق الحضور (Attendance Application)
**المسار**: `attendance/`
- نظام متقدم لتتبع حضور الموظفين
- تسجيل من مصادر متعددة (أجهزة، ويب، موبايل)
- قواعد الحضور المرنة والقابلة للتخصيص
- تقارير الحضور التفصيلية والإحصائيات
- تكامل مع أجهزة الحضور الخارجية

#### 10. تطبيق الإشعارات (Notifications Application)
**المسار**: `notifications/`
- نظام إشعارات شامل مع قنوات متعددة
- إشعارات فورية ومجدولة
- إدارة تفضيلات المستخدمين
- تتبع حالة الإشعارات والتسليم
- تكامل مع البريد الإلكتروني والرسائل النصية

### 🚗 التطبيقات الإضافية (Additional Applications)

#### 11. تطبيق إدارة السيارات (Cars Application)
**المسار**: `cars/`
- إدارة أسطول السيارات والمركبات
- جدولة الصيانة والخدمات
- تتبع الاستخدام والمصروفات
- إدارة التراخيص والتأمينات

#### 12. تطبيق أوامر الشراء (Purchase Orders Application)
**المسار**: `Purchase_orders/`
- إدارة طلبات الشراء مع سير عمل الموافقات
- ربط الطلبات بالمخزون والموردين
- تتبع حالة الطلبات والتسليم
- تقارير المشتريات والتكاليف

#### 13. تطبيق المهام العامة (Tasks Application)
**المسار**: `tasks/`
- إدارة المهام على مستوى النظام
- تكليف المهام للفرق والأقسام
- متابعة التقدم والإنجاز
- تكامل مع مهام الموظفين الشخصية

## الوثائق والمراجع (Documentation & References)

### 📚 دلائل مفصلة
- **[دليل API الكامل](api/README.md)** - شرح شامل لجميع endpoints والميزات
- **[دليل الموارد البشرية](Hr/README.md)** - توثيق شامل لنظام HR
- **[دليل إدارة المخزون](inventory/README.md)** - نظام إدارة المخزون
- **[دليل مهام الموظفين](employee_tasks/README.md)** - نظام المهام الشخصية
- **[دليل سجلات التدقيق](audit/README.md)** - نظام التدقيق والمراقبة

### 🔧 أدوات التطوير
- **[أمثلة API](api_examples.py)** - أمثلة عملية لاستخدام API
- **[إعداد تلقائي](setup_api.py)** - سكريبت إعداد API تلقائياً
- **[تشغيل الخادم](run_api_server.py)** - تشغيل خادم محسن للـ API

### 🤖 ميزات الذكاء الاصطناعي
- **محادثات ذكية**: استخدام Gemini AI للإجابة على الاستفسارات
- **تحليل البيانات**: تحليل ذكي لبيانات النظام واستخراج الرؤى
- **التوصيات**: توصيات ذكية لتحسين الأداء
- **المساعد الافتراضي**: مساعد ذكي لإدارة النظام

### 📊 نقاط النهاية الرئيسية
- `GET /api/v1/status/` - حالة النظام
- `GET /api/v1/employees/` - بيانات الموظفين
- `GET /api/v1/products/` - بيانات المنتجات
- `GET /api/v1/tasks/` - المهام
- `GET /api/v1/meetings/` - الاجتماعات
- `POST /api/v1/ai/chat/` - محادثة مع AI
- `POST /api/v1/ai/analyze/` - تحليل البيانات

## المساهمة في التطوير
- استخدم Git للتحكم في الإصدارات
- اتبع معايير كتابة الكود المعتمدة
- قم بإنشاء فرع جديد لكل ميزة أو إصلاح
- أرسل طلب سحب (Pull Request) للمراجعة قبل الدمج

## الترخيص
جميع الحقوق محفوظة © نظام الدولية
