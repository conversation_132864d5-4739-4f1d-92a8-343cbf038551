# Generated by Django 5.0.14 on 2025-06-22 11:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('meetings', '0004_meetingtaskstep'),
        ('tasks', '0005_fix_related_name_conflict'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='task',
            name='assigned_to',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_tasks', to=settings.AUTH_USER_MODEL, verbose_name='المكلف بالمهمة'),
        ),
        migrations.AlterField(
            model_name='task',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_tasks', to=settings.AUTH_USER_MODEL, verbose_name='منشئ المهمة'),
        ),
        migrations.AlterField(
            model_name='task',
            name='description',
            field=models.TextField(help_text='وصف تفصيلي للمهمة والمطلوب إنجازه', verbose_name='وصف المهمة'),
        ),
        migrations.AlterField(
            model_name='task',
            name='meeting',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='meetings.meeting', verbose_name='الاجتماع المرتبط'),
        ),
        migrations.AlterField(
            model_name='task',
            name='title',
            field=models.CharField(blank=True, help_text='عنوان مختصر للمهمة (اختياري)', max_length=200, null=True, verbose_name='عنوان المهمة'),
        ),
        migrations.AlterField(
            model_name='taskstep',
            name='date',
            field=models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء'),
        ),
        migrations.AlterField(
            model_name='taskstep',
            name='description',
            field=models.TextField(help_text='وصف تفصيلي للخطوة المتخذة', verbose_name='وصف الخطوة'),
        ),
        migrations.AlterField(
            model_name='taskstep',
            name='notes',
            field=models.TextField(blank=True, help_text='ملاحظات إضافية حول هذه الخطوة', null=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='taskstep',
            name='task',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='tasks.task', verbose_name='المهمة'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(fields=['status'], name='tasks_task_status_4a0a95_idx'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(fields=['assigned_to'], name='tasks_task_assigne_ab55af_idx'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(fields=['created_by'], name='tasks_task_created_d80085_idx'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(fields=['end_date'], name='tasks_task_end_dat_116fa9_idx'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(fields=['priority'], name='tasks_task_priorit_a900d4_idx'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(fields=['status', 'assigned_to'], name='tasks_task_status_d50342_idx'),
        ),
        migrations.AddIndex(
            model_name='task',
            index=models.Index(fields=['status', 'end_date'], name='tasks_task_status_527bb3_idx'),
        ),
        migrations.AddIndex(
            model_name='taskstep',
            index=models.Index(fields=['task'], name='tasks_tasks_task_id_279296_idx'),
        ),
        migrations.AddIndex(
            model_name='taskstep',
            index=models.Index(fields=['completed'], name='tasks_tasks_complet_f2effc_idx'),
        ),
        migrations.AddIndex(
            model_name='taskstep',
            index=models.Index(fields=['date'], name='tasks_tasks_date_8f75dc_idx'),
        ),
    ]
